package com.my.ai.aipro.config;

import com.my.ai.aipro.mapper.ChatSessionMapper;
import com.my.ai.aipro.repository.ChatSessionRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 配置测试类
 */
@SpringBootTest
public class ConfigurationTest {

    @Autowired(required = false)
    private ChatSessionMapper chatSessionMapper;

    @Autowired(required = false)
    private ChatSessionRepository chatSessionRepository;

    @Test
    public void testMyBatisPlusConfiguration() {
        // 测试MyBatis-Plus Mapper是否正确注入
        assertNotNull(chatSessionMapper, "ChatSessionMapper should be injected");
        System.out.println("MyBatis-Plus Mapper injected successfully: " + chatSessionMapper.getClass().getName());
    }

    @Test
    public void testJpaConfiguration() {
        // 测试JPA Repository是否正确注入
        assertNotNull(chatSessionRepository, "ChatSessionRepository should be injected");
        System.out.println("JPA Repository injected successfully: " + chatSessionRepository.getClass().getName());
    }
}
