package com.my.ai.aipro.config;

import com.my.ai.aipro.mapper.ChatSessionMapper;
import com.my.ai.aipro.repository.ChatSessionRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 配置测试类 - 测试JPA和MyBatis-Plus共存
 */
@SpringBootTest
@Transactional
public class ConfigurationTest {

    @Autowired(required = false)
    private ChatSessionMapper chatSessionMapper;

    @Autowired(required = false)
    private ChatSessionRepository chatSessionRepository;

    @Autowired
    private DataSource dataSource;

    @PersistenceContext
    private EntityManager entityManager;

    @Test
    public void testDataSourceConfiguration() {
        assertNotNull(dataSource, "DataSource should be injected");
        System.out.println("DataSource type: " + dataSource.getClass().getName());
    }

    @Test
    public void testJpaConfiguration() {
        // 测试JPA Repository是否正确注入
        assertNotNull(chatSessionRepository, "ChatSessionRepository should be injected");
        assertNotNull(entityManager, "EntityManager should be injected");
        System.out.println("JPA Repository injected successfully: " + chatSessionRepository.getClass().getName());
        System.out.println("EntityManager injected successfully: " + entityManager.getClass().getName());
    }

    @Test
    public void testMyBatisPlusConfiguration() {
        // 测试MyBatis-Plus Mapper是否正确注入
        assertNotNull(chatSessionMapper, "ChatSessionMapper should be injected");
        System.out.println("MyBatis-Plus Mapper injected successfully: " + chatSessionMapper.getClass().getName());
    }

    @Test
    public void testBothFrameworksCoexist() {
        // 测试两个框架是否能够共存
        assertNotNull(chatSessionRepository, "JPA Repository should work");
        assertNotNull(chatSessionMapper, "MyBatis-Plus Mapper should work");
        assertNotNull(entityManager, "JPA EntityManager should work");

        System.out.println("✅ JPA and MyBatis-Plus coexist successfully!");
        System.out.println("   - JPA Repository: " + chatSessionRepository.getClass().getSimpleName());
        System.out.println("   - MyBatis-Plus Mapper: " + chatSessionMapper.getClass().getSimpleName());
        System.out.println("   - EntityManager: " + entityManager.getClass().getSimpleName());
    }
}
