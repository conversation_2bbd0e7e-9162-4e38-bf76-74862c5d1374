package com.my.ai.aipro.integration;

import com.my.ai.aipro.entity.ChatSession;
import com.my.ai.aipro.entity.ChatSessionMp;
import com.my.ai.aipro.mapper.ChatSessionMapper;
import com.my.ai.aipro.repository.ChatSessionRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JPA和MyBatis-Plus集成测试
 */
@SpringBootTest
@Transactional
public class JpaMyBatisPlusIntegrationTest {

    @Autowired
    private ChatSessionRepository jpaRepository;

    @Autowired
    private ChatSessionMapper myBatisPlusMapper;

    @Test
    public void testJpaOperations() {
        // 测试JPA操作
        String conversationId = "jpa-test-" + UUID.randomUUID().toString();
        
        // 创建JPA实体
        ChatSession jpaSession = new ChatSession(conversationId, "jpa-user", "JPA测试会话");
        
        // 保存
        ChatSession saved = jpaRepository.save(jpaSession);
        assertNotNull(saved.getId());
        assertEquals(conversationId, saved.getConversationId());
        
        // 查询
        ChatSession found = jpaRepository.findByConversationId(conversationId).orElse(null);
        assertNotNull(found);
        assertEquals("JPA测试会话", found.getTitle());
        
        System.out.println("✅ JPA operations work correctly");
        System.out.println("   - Saved session ID: " + saved.getId());
        System.out.println("   - Found session: " + found.getTitle());
    }

    @Test
    public void testMyBatisPlusOperations() {
        // 测试MyBatis-Plus操作
        String conversationId = "mp-test-" + UUID.randomUUID().toString();
        
        // 创建MyBatis-Plus实体
        ChatSessionMp mpSession = new ChatSessionMp(conversationId, "mp-user", "MyBatis-Plus测试会话");
        
        // 保存
        int inserted = myBatisPlusMapper.insert(mpSession);
        assertEquals(1, inserted);
        assertNotNull(mpSession.getId());
        
        // 查询
        ChatSessionMp found = myBatisPlusMapper.findByConversationId(conversationId);
        assertNotNull(found);
        assertEquals("MyBatis-Plus测试会话", found.getTitle());
        
        System.out.println("✅ MyBatis-Plus operations work correctly");
        System.out.println("   - Inserted session ID: " + mpSession.getId());
        System.out.println("   - Found session: " + found.getTitle());
    }

    @Test
    public void testBothFrameworksSimultaneously() {
        // 测试两个框架同时工作
        String baseId = UUID.randomUUID().toString();
        String jpaConversationId = "jpa-" + baseId;
        String mpConversationId = "mp-" + baseId;
        
        // JPA操作
        ChatSession jpaSession = new ChatSession(jpaConversationId, "test-user", "JPA会话");
        jpaRepository.save(jpaSession);
        
        // MyBatis-Plus操作
        ChatSessionMp mpSession = new ChatSessionMp(mpConversationId, "test-user", "MyBatis-Plus会话");
        myBatisPlusMapper.insert(mpSession);
        
        // 验证两个都存在
        assertTrue(jpaRepository.existsByConversationId(jpaConversationId));
        assertTrue(myBatisPlusMapper.existsByConversationId(mpConversationId));
        
        // 统计数量
        long jpaCount = jpaRepository.countByUserIdAndIsActive("test-user", true);
        long mpCount = myBatisPlusMapper.countByUserIdAndIsActive("test-user", true);
        
        assertTrue(jpaCount >= 1);
        assertTrue(mpCount >= 1);
        
        System.out.println("✅ Both frameworks work simultaneously");
        System.out.println("   - JPA sessions for test-user: " + jpaCount);
        System.out.println("   - MyBatis-Plus sessions for test-user: " + mpCount);
    }

    @Test
    public void testDataConsistency() {
        // 测试数据一致性
        String conversationId = "consistency-test-" + UUID.randomUUID().toString();
        
        // 使用JPA创建
        ChatSession jpaSession = new ChatSession(conversationId, "consistency-user", "一致性测试");
        jpaRepository.save(jpaSession);
        
        // 使用MyBatis-Plus查询（应该能查到同一条记录）
        ChatSessionMp mpResult = myBatisPlusMapper.findByConversationId(conversationId);
        assertNotNull(mpResult, "MyBatis-Plus should find the record created by JPA");
        assertEquals("一致性测试", mpResult.getTitle());
        
        // 使用MyBatis-Plus更新
        mpResult.setTitle("更新后的标题");
        myBatisPlusMapper.updateById(mpResult);
        
        // 使用JPA查询（应该看到更新后的数据）
        ChatSession jpaResult = jpaRepository.findByConversationId(conversationId).orElse(null);
        assertNotNull(jpaResult, "JPA should find the record updated by MyBatis-Plus");
        assertEquals("更新后的标题", jpaResult.getTitle());
        
        System.out.println("✅ Data consistency maintained between frameworks");
        System.out.println("   - Original title: 一致性测试");
        System.out.println("   - Updated title: " + jpaResult.getTitle());
    }
}
