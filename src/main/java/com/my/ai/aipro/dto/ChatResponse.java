package com.my.ai.aipro.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * 聊天响应DTO
 */
public class ChatResponse {
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("conversationId")
    private String conversationId;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("tokensUsed")
    private Integer tokensUsed;
    
    public ChatResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ChatResponse(String message, String conversationId) {
        this();
        this.message = message;
        this.conversationId = conversationId;
        this.success = true;
    }
    
    public ChatResponse(String error) {
        this();
        this.error = error;
        this.success = false;
    }
    
    public static ChatResponse success(String message, String conversationId) {
        return new ChatResponse(message, conversationId);
    }
    
    public static ChatResponse error(String error) {
        return new ChatResponse(error);
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getConversationId() {
        return conversationId;
    }
    
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
    
    public Integer getTokensUsed() {
        return tokensUsed;
    }
    
    public void setTokensUsed(Integer tokensUsed) {
        this.tokensUsed = tokensUsed;
    }
    
    @Override
    public String toString() {
        return "ChatResponse{" +
                "message='" + message + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", timestamp=" + timestamp +
                ", success=" + success +
                ", error='" + error + '\'' +
                ", tokensUsed=" + tokensUsed +
                '}';
    }
}
