package com.my.ai.aipro.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.my.ai.aipro.dto.ChatRequest;
import com.my.ai.aipro.dto.ChatResponse;
import com.my.ai.aipro.entity.ChatMessageMp;
import com.my.ai.aipro.entity.ChatSessionMp;
import com.my.ai.aipro.service.ChatMessageMpService;
import com.my.ai.aipro.service.ChatSessionMpService;
import com.my.ai.aipro.service.DeepSeekChatMpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MyBatis-Plus版本的聊天控制器
 */
@RestController
@RequestMapping("/api/chat-mp")
@CrossOrigin(origins = "*")
public class ChatMpController {

    private static final Logger logger = LoggerFactory.getLogger(ChatMpController.class);

    private final DeepSeekChatMpService deepSeekChatMpService;
    private final ChatSessionMpService chatSessionMpService;
    private final ChatMessageMpService chatMessageMpService;

    @Autowired
    public ChatMpController(DeepSeekChatMpService deepSeekChatMpService,
                           ChatSessionMpService chatSessionMpService,
                           ChatMessageMpService chatMessageMpService) {
        this.deepSeekChatMpService = deepSeekChatMpService;
        this.chatSessionMpService = chatSessionMpService;
        this.chatMessageMpService = chatMessageMpService;
    }

    /**
     * 发送聊天消息 - MyBatis-Plus版本
     */
    @PostMapping("/send")
    public ResponseEntity<ChatResponse> sendMessage(@RequestBody ChatRequest request) {
        logger.info("Received chat request (MyBatis-Plus): {}", request);

        try {
            ChatResponse response = deepSeekChatMpService.chat(request);

            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error in sendMessage (MyBatis-Plus): {}", e.getMessage(), e);
            ChatResponse errorResponse = ChatResponse.error("服务器内部错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取会话历史
     */
    @GetMapping("/history/{conversationId}")
    public ResponseEntity<List<Map<String, String>>> getConversationHistory(@PathVariable String conversationId) {
        logger.info("Getting conversation history (MyBatis-Plus) for: {}", conversationId);

        try {
            List<Map<String, String>> history = deepSeekChatMpService.getConversationHistory(conversationId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            logger.error("Error getting conversation history (MyBatis-Plus): {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取会话详细信息
     */
    @GetMapping("/session/{conversationId}")
    public ResponseEntity<ChatSessionMp> getSessionInfo(@PathVariable String conversationId) {
        try {
            ChatSessionMp session = chatSessionMpService.findByConversationId(conversationId);
            if (session != null) {
                return ResponseEntity.ok(session);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error getting session info: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 分页获取用户会话列表
     */
    @GetMapping("/sessions")
    public ResponseEntity<IPage<ChatSessionMp>> getUserSessions(
            @RequestParam(defaultValue = "default_user") String userId,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            IPage<ChatSessionMp> sessions = chatSessionMpService.findActiveSessionsByUserId(userId, current, size);
            return ResponseEntity.ok(sessions);
        } catch (Exception e) {
            logger.error("Error getting user sessions: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 分页获取会话消息
     */
    @GetMapping("/messages/{conversationId}")
    public ResponseEntity<IPage<ChatMessageMp>> getConversationMessages(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "20") int size) {
        try {
            IPage<ChatMessageMp> messages = chatMessageMpService.getMessagesWithPagination(conversationId, current, size);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("Error getting conversation messages: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新会话标题
     */
    @PutMapping("/session/{conversationId}/title")
    public ResponseEntity<Map<String, Object>> updateSessionTitle(
            @PathVariable String conversationId,
            @RequestBody Map<String, String> request) {
        try {
            String title = request.get("title");
            boolean success = chatSessionMpService.updateSessionTitle(conversationId, title);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "标题更新成功" : "标题更新失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error updating session title: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", "更新标题失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search/{conversationId}")
    public ResponseEntity<List<ChatMessageMp>> searchMessages(
            @PathVariable String conversationId,
            @RequestParam String keyword) {
        try {
            List<ChatMessageMp> messages = chatMessageMpService.searchMessages(conversationId, keyword);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("Error searching messages: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取会话统计信息
     */
    @GetMapping("/stats/{conversationId}")
    public ResponseEntity<Map<String, Object>> getConversationStats(@PathVariable String conversationId) {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 消息数量
            long messageCount = chatMessageMpService.countConversationMessages(conversationId);
            stats.put("messageCount", messageCount);
            
            // Token使用量
            Long tokensUsed = chatMessageMpService.getTotalTokensUsed(conversationId);
            stats.put("tokensUsed", tokensUsed != null ? tokensUsed : 0);
            
            // 各角色消息数量
            List<Map<String, Object>> roleStats = chatMessageMpService.countMessagesByRole(conversationId);
            stats.put("roleStats", roleStats);
            
            // 最后一条消息
            ChatMessageMp lastMessage = chatMessageMpService.getLastMessage(conversationId);
            stats.put("lastMessage", lastMessage);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error getting conversation stats: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 清除会话历史
     */
    @DeleteMapping("/history/{conversationId}")
    public ResponseEntity<Map<String, String>> clearConversationHistory(@PathVariable String conversationId) {
        logger.info("Clearing conversation history (MyBatis-Plus) for: {}", conversationId);

        try {
            deepSeekChatMpService.clearConversationHistory(conversationId);
            return ResponseEntity.ok(Map.of("message", "会话历史已清除", "conversationId", conversationId));
        } catch (Exception e) {
            logger.error("Error clearing conversation history (MyBatis-Plus): {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清除会话历史失败: " + e.getMessage()));
        }
    }

    /**
     * 清除所有会话历史
     */
    @DeleteMapping("/history")
    public ResponseEntity<Map<String, String>> clearAllConversations() {
        logger.info("Clearing all conversation histories (MyBatis-Plus)");

        try {
            deepSeekChatMpService.clearAllConversations();
            return ResponseEntity.ok(Map.of("message", "所有会话历史已清除"));
        } catch (Exception e) {
            logger.error("Error clearing all conversations (MyBatis-Plus): {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清除所有会话历史失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            int activeConversations = deepSeekChatMpService.getActiveConversationsCount();
            return ResponseEntity.ok(Map.of(
                    "status", "运行中 (MyBatis-Plus)",
                    "activeConversations", activeConversations,
                    "timestamp", System.currentTimeMillis(),
                    "version", "MyBatis-Plus Enhanced"
            ));
        } catch (Exception e) {
            logger.error("Error getting status (MyBatis-Plus): {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取状态失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of("status", "UP", "version", "MyBatis-Plus"));
    }
}
