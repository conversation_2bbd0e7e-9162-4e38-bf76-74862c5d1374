package com.my.ai.aipro.controller;

import com.my.ai.aipro.dto.ChatRequest;
import com.my.ai.aipro.dto.ChatResponse;
import com.my.ai.aipro.service.DeepSeekChatService;
import com.my.ai.aipro.service.HealthCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * AI聊天控制器
 */
@RestController
@RequestMapping("/api/chat")
@CrossOrigin(origins = "*")
public class AiChatController {

    private static final Logger logger = LoggerFactory.getLogger(AiChatController.class);

    private final DeepSeekChatService deepSeekChatService;
    private final HealthCheckService healthCheckService;

    @Autowired
    public AiChatController(DeepSeekChatService deepSeekChatService, HealthCheckService healthCheckService) {
        this.deepSeekChatService = deepSeekChatService;
        this.healthCheckService = healthCheckService;
    }

    /**
     * 发送聊天消息
     */
    @PostMapping("/send")
    public ResponseEntity<ChatResponse> sendMessage(@RequestBody ChatRequest request) {
        logger.info("Received chat request: {}", request);

        try {
            ChatResponse response = deepSeekChatService.chat(request);

            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error in sendMessage: {}", e.getMessage(), e);
            ChatResponse errorResponse = ChatResponse.error("服务器内部错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 发送聊天消息,打字机效果
     */
    @PostMapping(path = "/sendFlux",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> sendMessageFlux(@RequestBody ChatRequest request) {
        logger.info("Received chat request: {}", request);

        try {
            ChatResponse response = deepSeekChatService.chat(request);

            if (response.isSuccess()) {
                return Flux.fromArray(response.getMessage().split(""))
                        .delayElements(Duration.ofMillis(delay));
            } else {
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error in sendMessage: {}", e.getMessage(), e);
            ChatResponse errorResponse = ChatResponse.error("服务器内部错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }


    /**
     * 获取会话历史
     */
    @GetMapping("/history/{conversationId}")
    public ResponseEntity<List<Map<String, String>>> getConversationHistory(@PathVariable String conversationId) {
        logger.info("Getting conversation history for: {}", conversationId);

        try {
            List<Map<String, String>> history = deepSeekChatService.getConversationHistory(conversationId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            logger.error("Error getting conversation history: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 清除会话历史
     */
    @DeleteMapping("/history/{conversationId}")
    public ResponseEntity<Map<String, String>> clearConversationHistory(@PathVariable String conversationId) {
        logger.info("Clearing conversation history for: {}", conversationId);

        try {
            deepSeekChatService.clearConversationHistory(conversationId);
            return ResponseEntity.ok(Map.of("message", "会话历史已清除", "conversationId", conversationId));
        } catch (Exception e) {
            logger.error("Error clearing conversation history: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清除会话历史失败: " + e.getMessage()));
        }
    }

    /**
     * 清除所有会话历史
     */
    @DeleteMapping("/history")
    public ResponseEntity<Map<String, String>> clearAllConversations() {
        logger.info("Clearing all conversation histories");

        try {
            deepSeekChatService.clearAllConversations();
            return ResponseEntity.ok(Map.of("message", "所有会话历史已清除"));
        } catch (Exception e) {
            logger.error("Error clearing all conversations: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清除所有会话历史失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            int activeConversations = deepSeekChatService.getActiveConversationsCount();
            return ResponseEntity.ok(Map.of(
                    "status", "运行中",
                    "activeConversations", activeConversations,
                    "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            logger.error("Error getting status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取状态失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of("status", "UP"));
    }

    /**
     * 详细健康检查
     */
    @GetMapping("/health/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        try {
            Map<String, Object> healthStatus = healthCheckService.checkAllServices();
            return ResponseEntity.ok(healthStatus);
        } catch (Exception e) {
            logger.error("Error in detailed health check: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "健康检查失败: " + e.getMessage()));
        }
    }
}
