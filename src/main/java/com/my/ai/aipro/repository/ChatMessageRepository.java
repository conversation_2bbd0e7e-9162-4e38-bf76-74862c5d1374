package com.my.ai.aipro.repository;

import com.my.ai.aipro.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息Repository
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * 根据会话ID查找所有消息，按创建时间排序
     */
    List<ChatMessage> findByConversationIdOrderByCreatedAtAsc(String conversationId);

    /**
     * 根据会话ID查找最近的N条消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversationId = :conversationId ORDER BY cm.createdAt DESC")
    List<ChatMessage> findRecentMessagesByConversationId(@Param("conversationId") String conversationId);

    /**
     * 统计会话的消息数量
     */
    long countByConversationId(String conversationId);

    /**
     * 删除指定会话的所有消息
     */
    void deleteByConversationId(String conversationId);

    /**
     * 查找指定时间之前的消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.createdAt < :beforeTime")
    List<ChatMessage> findMessagesCreatedBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据会话ID和角色查找消息
     */
    List<ChatMessage> findByConversationIdAndRoleOrderByCreatedAtAsc(String conversationId, String role);

    /**
     * 统计总token使用量
     */
    @Query("SELECT SUM(cm.tokensUsed) FROM ChatMessage cm WHERE cm.conversationId = :conversationId AND cm.tokensUsed IS NOT NULL")
    Long sumTokensUsedByConversationId(@Param("conversationId") String conversationId);
}
