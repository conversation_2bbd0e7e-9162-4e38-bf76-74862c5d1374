package com.my.ai.aipro.repository;

import com.my.ai.aipro.entity.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 聊天会话Repository
 */
@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, Long> {

    /**
     * 根据会话ID查找会话
     */
    Optional<ChatSession> findByConversationId(String conversationId);

    /**
     * 根据用户ID查找所有活跃会话
     */
    List<ChatSession> findByUserIdAndIsActiveOrderByUpdatedAtDesc(String userId, Boolean isActive);

    /**
     * 根据用户ID查找所有会话
     */
    List<ChatSession> findByUserIdOrderByUpdatedAtDesc(String userId);

    /**
     * 查找指定时间之前的会话
     */
    @Query("SELECT cs FROM ChatSession cs WHERE cs.updatedAt < :beforeTime")
    List<ChatSession> findSessionsUpdatedBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计用户的会话数量
     */
    long countByUserIdAndIsActive(String userId, Boolean isActive);

    /**
     * 删除指定会话ID的会话
     */
    void deleteByConversationId(String conversationId);

    /**
     * 检查会话是否存在
     */
    boolean existsByConversationId(String conversationId);
}
