package com.my.ai.aipro.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * JPA配置类
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.my.ai.aipro.repository")
@EntityScan(basePackages = "com.my.ai.aipro.entity")
public class JpaConfig {
}
