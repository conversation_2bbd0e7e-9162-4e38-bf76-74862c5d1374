package com.my.ai.aipro.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.ai.aipro.entity.ChatMessageMp;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息Mapper - MyBatis-Plus版本
 */
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessageMp> {

    /**
     * 根据会话ID查找所有消息，按创建时间排序
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    List<ChatMessageMp> findByConversationIdOrderByCreatedAtAsc(@Param("conversationId") String conversationId);

    /**
     * 根据会话ID查找最近的N条消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT #{limit}")
    List<ChatMessageMp> findRecentMessagesByConversationId(@Param("conversationId") String conversationId, 
                                                           @Param("limit") Integer limit);

    /**
     * 分页查询会话消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    IPage<ChatMessageMp> findMessagesByConversationId(Page<ChatMessageMp> page, 
                                                      @Param("conversationId") String conversationId);

    /**
     * 统计会话的消息数量
     */
    @Select("SELECT COUNT(*) FROM chat_messages WHERE conversation_id = #{conversationId}")
    long countByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据会话ID删除所有消息
     */
    @Delete("DELETE FROM chat_messages WHERE conversation_id = #{conversationId}")
    int deleteByConversationId(@Param("conversationId") String conversationId);

    /**
     * 查找指定时间之前的消息
     */
    @Select("SELECT * FROM chat_messages WHERE created_at < #{beforeTime}")
    List<ChatMessageMp> findMessagesCreatedBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据会话ID和角色查找消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} AND role = #{role} ORDER BY created_at ASC")
    List<ChatMessageMp> findByConversationIdAndRole(@Param("conversationId") String conversationId, 
                                                    @Param("role") String role);

    /**
     * 统计总token使用量
     */
    @Select("SELECT COALESCE(SUM(tokens_used), 0) FROM chat_messages WHERE conversation_id = #{conversationId} AND tokens_used IS NOT NULL")
    Long sumTokensUsedByConversationId(@Param("conversationId") String conversationId);

    /**
     * 获取用户的总token使用量
     */
    @Select("SELECT COALESCE(SUM(cm.tokens_used), 0) FROM chat_messages cm " +
            "JOIN chat_sessions cs ON cm.conversation_id = cs.conversation_id " +
            "WHERE cs.user_id = #{userId} AND cm.tokens_used IS NOT NULL")
    Long sumTokensUsedByUserId(@Param("userId") String userId);

    /**
     * 获取指定时间范围内的消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} " +
            "AND created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at ASC")
    List<ChatMessageMp> findMessagesByTimeRange(@Param("conversationId") String conversationId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 批量插入消息
     */
    @Insert("<script>" +
            "INSERT INTO chat_messages (conversation_id, role, content, created_at, tokens_used, model_used, temperature, max_tokens) VALUES " +
            "<foreach collection='messages' item='msg' separator=','>" +
            "(#{msg.conversationId}, #{msg.role}, #{msg.content}, #{msg.createdAt}, #{msg.tokensUsed}, #{msg.modelUsed}, #{msg.temperature}, #{msg.maxTokens})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("messages") List<ChatMessageMp> messages);

    /**
     * 获取会话中最后一条消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT 1")
    ChatMessageMp findLastMessageByConversationId(@Param("conversationId") String conversationId);

    /**
     * 统计各角色的消息数量
     */
    @Select("SELECT role, COUNT(*) as count FROM chat_messages WHERE conversation_id = #{conversationId} GROUP BY role")
    @Results({
        @Result(column = "role", property = "role"),
        @Result(column = "count", property = "count")
    })
    List<java.util.Map<String, Object>> countMessagesByRole(@Param("conversationId") String conversationId);

    /**
     * 搜索消息内容
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} " +
            "AND content LIKE CONCAT('%', #{keyword}, '%') ORDER BY created_at ASC")
    List<ChatMessageMp> searchMessagesByKeyword(@Param("conversationId") String conversationId, 
                                               @Param("keyword") String keyword);
}
