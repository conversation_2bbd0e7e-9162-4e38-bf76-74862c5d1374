package com.my.ai.aipro.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.ai.aipro.entity.ChatSessionMp;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天会话Mapper - MyBatis-Plus版本
 */
@Mapper
public interface ChatSessionMapper extends BaseMapper<ChatSessionMp> {

    /**
     * 根据会话ID查找会话
     */
    @Select("SELECT * FROM chat_sessions WHERE conversation_id = #{conversationId}")
    ChatSessionMp findByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据用户ID查找活跃会话（分页）
     */
    @Select("SELECT * FROM chat_sessions WHERE user_id = #{userId} AND is_active = #{isActive} ORDER BY updated_at DESC")
    IPage<ChatSessionMp> findByUserIdAndIsActive(Page<ChatSessionMp> page, 
                                                 @Param("userId") String userId, 
                                                 @Param("isActive") Boolean isActive);

    /**
     * 根据用户ID查找所有会话
     */
    @Select("SELECT * FROM chat_sessions WHERE user_id = #{userId} ORDER BY updated_at DESC")
    List<ChatSessionMp> findByUserId(@Param("userId") String userId);

    /**
     * 查找指定时间之前的会话
     */
    @Select("SELECT * FROM chat_sessions WHERE updated_at < #{beforeTime}")
    List<ChatSessionMp> findSessionsUpdatedBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计用户的会话数量
     */
    @Select("SELECT COUNT(*) FROM chat_sessions WHERE user_id = #{userId} AND is_active = #{isActive}")
    long countByUserIdAndIsActive(@Param("userId") String userId, @Param("isActive") Boolean isActive);

    /**
     * 根据会话ID删除会话
     */
    @Delete("DELETE FROM chat_sessions WHERE conversation_id = #{conversationId}")
    int deleteByConversationId(@Param("conversationId") String conversationId);

    /**
     * 检查会话是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM chat_sessions WHERE conversation_id = #{conversationId}")
    boolean existsByConversationId(@Param("conversationId") String conversationId);

    /**
     * 更新会话消息数量
     */
    @Update("UPDATE chat_sessions SET message_count = message_count + #{increment}, updated_at = NOW() WHERE conversation_id = #{conversationId}")
    int incrementMessageCount(@Param("conversationId") String conversationId, @Param("increment") Integer increment);

    /**
     * 批量更新会话状态
     */
    @Update("UPDATE chat_sessions SET is_active = #{isActive}, updated_at = NOW() WHERE user_id = #{userId}")
    int updateActiveStatusByUserId(@Param("userId") String userId, @Param("isActive") Boolean isActive);

    /**
     * 获取用户最近的会话
     */
    @Select("SELECT * FROM chat_sessions WHERE user_id = #{userId} AND is_active = true ORDER BY updated_at DESC LIMIT #{limit}")
    List<ChatSessionMp> findRecentSessions(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 复杂查询：根据多个条件查找会话
     */
    @Select("<script>" +
            "SELECT * FROM chat_sessions WHERE 1=1 " +
            "<if test='userId != null and userId != \"\"'> AND user_id = #{userId} </if>" +
            "<if test='isActive != null'> AND is_active = #{isActive} </if>" +
            "<if test='title != null and title != \"\"'> AND title LIKE CONCAT('%', #{title}, '%') </if>" +
            "<if test='startTime != null'> AND created_at >= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_at <= #{endTime} </if>" +
            "ORDER BY updated_at DESC" +
            "</script>")
    List<ChatSessionMp> findSessionsByConditions(@Param("userId") String userId,
                                                @Param("isActive") Boolean isActive,
                                                @Param("title") String title,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);
}
