package com.my.ai.aipro.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.my.ai.aipro.dto.ChatRequest;
import com.my.ai.aipro.dto.ChatResponse;
import com.my.ai.aipro.entity.ChatMessageMp;
import com.my.ai.aipro.entity.ChatSessionMp;
import com.my.ai.aipro.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * DeepSeek聊天服务 - MyBatis-Plus增强版
 */
@Service
public class DeepSeekChatMpService {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekChatMpService.class);

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final RedisUtil redisUtil;
    private final ChatSessionMpService chatSessionMpService;
    private final ChatMessageMpService chatMessageMpService;

    @Value("${deepseek.api.key:***********************************}")
    private String apiKey;

    @Value("${deepseek.api.url:https://api.deepseek.com/v1/chat/completions}")
    private String apiUrl;

    @Value("${deepseek.model:deepseek-chat}")
    private String model;

    // Redis缓存键前缀
    private static final String CONVERSATION_CACHE_PREFIX = "conversation:mp:";
    private static final int CACHE_EXPIRE_HOURS = 24;

    @Autowired
    public DeepSeekChatMpService(RedisUtil redisUtil,
                                ChatSessionMpService chatSessionMpService,
                                ChatMessageMpService chatMessageMpService) {
        this.redisUtil = redisUtil;
        this.chatSessionMpService = chatSessionMpService;
        this.chatMessageMpService = chatMessageMpService;
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 发送聊天消息 - MyBatis-Plus版本
     */
    @Transactional
    public ChatResponse chat(ChatRequest request) {
        try {
            logger.info("Processing chat request with MyBatis-Plus: {}", request);

            // 验证请求
            if (request.getMessage() == null || request.getMessage().trim().isEmpty()) {
                return ChatResponse.error("消息内容不能为空");
            }

            // 获取或创建会话ID
            String conversationId = request.getConversationId();
            if (conversationId == null || conversationId.trim().isEmpty()) {
                conversationId = UUID.randomUUID().toString();
            }

            // 确保会话存在
            ChatSessionMp session = chatSessionMpService.ensureSessionExists(conversationId, "default_user");

            // 保存用户消息到数据库
            chatMessageMpService.saveUserMessage(conversationId, request.getMessage(),
                    request.getTemperature(), request.getMaxTokens(), model);

            // 获取会话历史（优先从Redis缓存获取）
            List<Map<String, String>> messages = getConversationHistoryFromCache(conversationId);

            // 添加当前用户消息到历史
            Map<String, String> userMsgMap = new HashMap<>();
            userMsgMap.put("role", "user");
            userMsgMap.put("content", request.getMessage());
            messages.add(userMsgMap);

            // 限制历史消息数量（保留最近20条消息）
            if (messages.size() > 20) {
                messages = messages.subList(messages.size() - 20, messages.size());
            }

            // 构建请求体
            ObjectNode requestBody = buildApiRequest(messages, request);

            // 调用DeepSeek API
            String responseBody = callDeepSeekApi(requestBody);

            if (responseBody != null) {
                JsonNode responseJson = objectMapper.readTree(responseBody);
                JsonNode choices = responseJson.get("choices");

                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode message = firstChoice.get("message");
                    String responseContent = message.get("content").asText();

                    // 获取token使用情况
                    Integer tokensUsed = null;
                    JsonNode usage = responseJson.get("usage");
                    if (usage != null && usage.has("total_tokens")) {
                        tokensUsed = usage.get("total_tokens").asInt();
                    }

                    // 保存助手消息到数据库
                    chatMessageMpService.saveAssistantMessage(conversationId, responseContent, model, tokensUsed);

                    // 添加助手消息到历史并更新缓存
                    Map<String, String> assistantMsgMap = new HashMap<>();
                    assistantMsgMap.put("role", "assistant");
                    assistantMsgMap.put("content", responseContent);
                    messages.add(assistantMsgMap);

                    // 更新Redis缓存
                    updateConversationCache(conversationId, messages);

                    // 更新会话信息
                    chatSessionMpService.incrementMessageCount(conversationId, 2); // 用户消息 + AI回复

                    // 创建响应
                    ChatResponse response = ChatResponse.success(responseContent, conversationId);
                    response.setTokensUsed(tokensUsed);

                    logger.info("Chat response generated successfully with MyBatis-Plus for conversation: {}", conversationId);
                    return response;
                } else {
                    logger.error("No choices in API response");
                    return ChatResponse.error("API响应格式错误");
                }
            } else {
                logger.error("API returned null response");
                return ChatResponse.error("API服务返回空响应");
            }

        } catch (Exception e) {
            logger.error("Error processing chat request with MyBatis-Plus: {}", e.getMessage(), e);
            return ChatResponse.error("处理聊天请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 从缓存获取会话历史
     */
    private List<Map<String, String>> getConversationHistoryFromCache(String conversationId) {
        String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;

        try {
            // 尝试从Redis获取
            Object cached = redisUtil.get(cacheKey);
            if (cached != null && cached instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> cachedMessages = (List<Map<String, String>>) cached;
                logger.debug("Retrieved conversation history from Redis cache: {}", conversationId);
                return cachedMessages;
            }
        } catch (Exception e) {
            logger.warn("Failed to get conversation history from Redis: {}", e.getMessage());
        }

        // 从数据库获取
        List<Map<String, String>> messages = chatMessageMpService.getConversationHistoryAsMap(conversationId);

        // 更新Redis缓存
        if (!messages.isEmpty()) {
            updateConversationCache(conversationId, messages);
        }

        logger.debug("Retrieved conversation history from database: {}", conversationId);
        return messages;
    }

    /**
     * 更新会话缓存
     */
    private void updateConversationCache(String conversationId, List<Map<String, String>> messages) {
        String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
        try {
            redisUtil.set(cacheKey, messages, CACHE_EXPIRE_HOURS * 3600);
            logger.debug("Updated conversation cache: {}", conversationId);
        } catch (Exception e) {
            logger.warn("Failed to update conversation cache: {}", e.getMessage());
        }
    }

    /**
     * 构建API请求体
     */
    private ObjectNode buildApiRequest(List<Map<String, String>> messages, ChatRequest request) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("model", model);
        requestBody.put("temperature", request.getTemperature() != null ? request.getTemperature() : 0.7);
        requestBody.put("max_tokens", request.getMaxTokens() != null ? request.getMaxTokens() : 1000);

        ArrayNode messagesArray = objectMapper.createArrayNode();
        for (Map<String, String> msg : messages) {
            ObjectNode msgNode = objectMapper.createObjectNode();
            msgNode.put("role", msg.get("role"));
            msgNode.put("content", msg.get("content"));
            messagesArray.add(msgNode);
        }
        requestBody.set("messages", messagesArray);

        return requestBody;
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekApi(ObjectNode requestBody) {
        Mono<String> responseMono = webClient.post()
                .uri(apiUrl)
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class);

        return responseMono.block();
    }

    /**
     * 获取会话历史
     */
    public List<Map<String, String>> getConversationHistory(String conversationId) {
        return getConversationHistoryFromCache(conversationId);
    }

    /**
     * 清除会话历史
     */
    @Transactional
    public void clearConversationHistory(String conversationId) {
        try {
            // 从数据库删除消息
            chatMessageMpService.deleteConversationMessages(conversationId);

            // 停用会话
            chatSessionMpService.deactivateSession(conversationId);

            // 从Redis缓存删除
            String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
            redisUtil.del(cacheKey);

            logger.info("Cleared conversation history with MyBatis-Plus for: {}", conversationId);
        } catch (Exception e) {
            logger.error("Failed to clear conversation history with MyBatis-Plus: {}", e.getMessage(), e);
            throw new RuntimeException("清除会话历史失败", e);
        }
    }

    /**
     * 清除所有会话历史
     */
    @Transactional
    public void clearAllConversations() {
        try {
            // 停用所有用户会话
            chatSessionMpService.deactivateAllUserSessions("default_user");

            logger.info("Cleared all conversation histories with MyBatis-Plus");
        } catch (Exception e) {
            logger.error("Failed to clear all conversations with MyBatis-Plus: {}", e.getMessage(), e);
            throw new RuntimeException("清除所有会话历史失败", e);
        }
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveConversationsCount() {
        return (int) chatSessionMpService.countActiveSessionsByUserId("default_user");
    }
}
