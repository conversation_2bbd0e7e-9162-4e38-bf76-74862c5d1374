package com.my.ai.aipro.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.my.ai.aipro.dto.ChatRequest;
import com.my.ai.aipro.dto.ChatResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * DeepSeek聊天服务
 */
@Service
public class DeepSeekChatService {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekChatService.class);

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    @Value("${deepseek.api.key:***********************************}")
    private String apiKey;

    @Value("${deepseek.api.url:https://api.deepseek.com/v1/chat/completions}")
    private String apiUrl;

    @Value("${deepseek.model:deepseek-chat}")
    private String model;

    // 存储会话历史，实际项目中应该使用数据库或Redis
    private final Map<String, List<Map<String, String>>> conversationHistory = new ConcurrentHashMap<>();

    public DeepSeekChatService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 发送聊天消息
     */
    public ChatResponse chat(ChatRequest request) {
        try {
            logger.info("Processing chat request: {}", request);

            // 验证请求
            if (request.getMessage() == null || request.getMessage().trim().isEmpty()) {
                return ChatResponse.error("消息内容不能为空");
            }

            // 获取或创建会话ID
            String conversationId = request.getConversationId();
            if (conversationId == null || conversationId.trim().isEmpty()) {
                conversationId = UUID.randomUUID().toString();
            }

            // 获取会话历史
            List<Map<String, String>> messages = conversationHistory.computeIfAbsent(conversationId, k -> new ArrayList<>());

            // 添加用户消息
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", request.getMessage());
            messages.add(userMessage);

            // 限制历史消息数量（保留最近20条消息）
            if (messages.size() > 20) {
                messages = messages.subList(messages.size() - 20, messages.size());
                conversationHistory.put(conversationId, messages);
            }

            // 构建请求体
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", model);
            requestBody.put("temperature", request.getTemperature() != null ? request.getTemperature() : 0.7);
            requestBody.put("max_tokens", request.getMaxTokens() != null ? request.getMaxTokens() : 1000);

            ArrayNode messagesArray = objectMapper.createArrayNode();
            for (Map<String, String> msg : messages) {
                ObjectNode msgNode = objectMapper.createObjectNode();
                msgNode.put("role", msg.get("role"));
                msgNode.put("content", msg.get("content"));
                messagesArray.add(msgNode);
            }
            requestBody.set("messages", messagesArray);

            // 调用DeepSeek API
            Mono<String> responseMono = webClient.post()
                    .uri(apiUrl)
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class);

            String responseBody = responseMono.block();

            if (responseBody != null) {
                JsonNode responseJson = objectMapper.readTree(responseBody);
                JsonNode choices = responseJson.get("choices");

                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode message = firstChoice.get("message");
                    String responseContent = message.get("content").asText();

                    // 添加助手消息到历史
                    Map<String, String> assistantMessage = new HashMap<>();
                    assistantMessage.put("role", "assistant");
                    assistantMessage.put("content", responseContent);
                    messages.add(assistantMessage);

                    // 创建响应
                    ChatResponse response = ChatResponse.success(responseContent, conversationId);

                    // 设置token使用情况（如果可用）
                    JsonNode usage = responseJson.get("usage");
                    if (usage != null && usage.has("total_tokens")) {
                        response.setTokensUsed(usage.get("total_tokens").asInt());
                    }

                    logger.info("Chat response generated successfully for conversation: {}", conversationId);
                    return response;
                } else {
                    logger.error("No choices in API response");
                    return ChatResponse.error("API响应格式错误");
                }
            } else {
                logger.error("API returned null response");
                return ChatResponse.error("API服务返回空响应");
            }

        } catch (Exception e) {
            logger.error("Error processing chat request: {}", e.getMessage(), e);
            return ChatResponse.error("处理聊天请求时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取会话历史
     */
    public List<Map<String, String>> getConversationHistory(String conversationId) {
        return conversationHistory.getOrDefault(conversationId, new ArrayList<>());
    }
    
    /**
     * 清除会话历史
     */
    public void clearConversationHistory(String conversationId) {
        conversationHistory.remove(conversationId);
        logger.info("Cleared conversation history for: {}", conversationId);
    }
    
    /**
     * 清除所有会话历史
     */
    public void clearAllConversations() {
        conversationHistory.clear();
        logger.info("Cleared all conversation histories");
    }
    
    /**
     * 获取活跃会话数量
     */
    public int getActiveConversationsCount() {
        return conversationHistory.size();
    }
}
