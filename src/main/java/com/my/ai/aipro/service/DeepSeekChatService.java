package com.my.ai.aipro.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.my.ai.aipro.dto.ChatRequest;
import com.my.ai.aipro.dto.ChatResponse;
import com.my.ai.aipro.entity.ChatMessage;
import com.my.ai.aipro.entity.ChatSession;
import com.my.ai.aipro.repository.ChatMessageRepository;
import com.my.ai.aipro.repository.ChatSessionRepository;
import com.my.ai.aipro.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * DeepSeek聊天服务
 */
@Service
public class DeepSeekChatService {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekChatService.class);

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final RedisUtil redisUtil;
    private final ChatSessionRepository chatSessionRepository;
    private final ChatMessageRepository chatMessageRepository;

    @Value("${deepseek.api.key:***********************************}")
    private String apiKey;

    @Value("${deepseek.api.url:https://api.deepseek.com/v1/chat/completions}")
    private String apiUrl;

    @Value("${deepseek.model:deepseek-chat}")
    private String model;

    // Redis缓存键前缀
    private static final String CONVERSATION_CACHE_PREFIX = "conversation:";
    private static final int CACHE_EXPIRE_HOURS = 24; // 缓存24小时

    // 内存备份（当Redis不可用时）
    private final Map<String, List<Map<String, String>>> conversationHistory = new ConcurrentHashMap<>();

    @Autowired
    public DeepSeekChatService(RedisUtil redisUtil,
                              ChatSessionRepository chatSessionRepository,
                              ChatMessageRepository chatMessageRepository) {
        this.redisUtil = redisUtil;
        this.chatSessionRepository = chatSessionRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 发送聊天消息
     */
    @Transactional
    public ChatResponse chat(ChatRequest request) {
        try {
            logger.info("Processing chat request: {}", request);

            // 验证请求
            if (request.getMessage() == null || request.getMessage().trim().isEmpty()) {
                return ChatResponse.error("消息内容不能为空");
            }

            // 获取或创建会话ID
            String conversationId = request.getConversationId();
            if (conversationId == null || conversationId.trim().isEmpty()) {
                conversationId = UUID.randomUUID().toString();
            }

            // 确保会话存在
            ensureSessionExists(conversationId, "default_user");

            // 保存用户消息到数据库
            ChatMessage userMessage = new ChatMessage(conversationId, "user", request.getMessage());
            userMessage.setTemperature(request.getTemperature());
            userMessage.setMaxTokens(request.getMaxTokens());
            userMessage.setModelUsed(model);
            chatMessageRepository.save(userMessage);

            // 获取会话历史（优先从Redis缓存获取）
            List<Map<String, String>> messages = getConversationHistory(conversationId);

            // 添加当前用户消息到历史
            Map<String, String> userMsgMap = new HashMap<>();
            userMsgMap.put("role", "user");
            userMsgMap.put("content", request.getMessage());
            messages.add(userMsgMap);

            // 限制历史消息数量（保留最近20条消息）
            if (messages.size() > 20) {
                messages = messages.subList(messages.size() - 20, messages.size());
            }

            // 构建请求体
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", model);
            requestBody.put("temperature", request.getTemperature() != null ? request.getTemperature() : 0.7);
            requestBody.put("max_tokens", request.getMaxTokens() != null ? request.getMaxTokens() : 1000);

            ArrayNode messagesArray = objectMapper.createArrayNode();
            for (Map<String, String> msg : messages) {
                ObjectNode msgNode = objectMapper.createObjectNode();
                msgNode.put("role", msg.get("role"));
                msgNode.put("content", msg.get("content"));
                messagesArray.add(msgNode);
            }
            requestBody.set("messages", messagesArray);

            // 调用DeepSeek API
            Mono<String> responseMono = webClient.post()
                    .uri(apiUrl)
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class);

            String responseBody = responseMono.block();

            if (responseBody != null) {
                JsonNode responseJson = objectMapper.readTree(responseBody);
                JsonNode choices = responseJson.get("choices");

                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode message = firstChoice.get("message");
                    String responseContent = message.get("content").asText();

                    // 保存助手消息到数据库
                    ChatMessage assistantMessage = new ChatMessage(conversationId, "assistant", responseContent);
                    assistantMessage.setModelUsed(model);

                    // 设置token使用情况（如果可用）
                    JsonNode usage = responseJson.get("usage");
                    Integer tokensUsed = null;
                    if (usage != null && usage.has("total_tokens")) {
                        tokensUsed = usage.get("total_tokens").asInt();
                        assistantMessage.setTokensUsed(tokensUsed);
                    }

                    chatMessageRepository.save(assistantMessage);

                    // 添加助手消息到历史并更新缓存
                    Map<String, String> assistantMsgMap = new HashMap<>();
                    assistantMsgMap.put("role", "assistant");
                    assistantMsgMap.put("content", responseContent);
                    messages.add(assistantMsgMap);

                    // 更新Redis缓存
                    updateConversationCache(conversationId, messages);

                    // 更新会话信息
                    updateSessionInfo(conversationId);

                    // 创建响应
                    ChatResponse response = ChatResponse.success(responseContent, conversationId);
                    response.setTokensUsed(tokensUsed);

                    logger.info("Chat response generated successfully for conversation: {}", conversationId);
                    return response;
                } else {
                    logger.error("No choices in API response");
                    return ChatResponse.error("API响应格式错误");
                }
            } else {
                logger.error("API returned null response");
                return ChatResponse.error("API服务返回空响应");
            }

        } catch (Exception e) {
            logger.error("Error processing chat request: {}", e.getMessage(), e);
            return ChatResponse.error("处理聊天请求时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取会话历史（优先从Redis缓存获取，然后从数据库获取）
     */
    public List<Map<String, String>> getConversationHistory(String conversationId) {
        String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;

        try {
            // 尝试从Redis获取
            Object cached = redisUtil.get(cacheKey);
            if (cached != null && cached instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> cachedMessages = (List<Map<String, String>>) cached;
                logger.debug("Retrieved conversation history from Redis cache: {}", conversationId);
                return cachedMessages;
            }
        } catch (Exception e) {
            logger.warn("Failed to get conversation history from Redis: {}", e.getMessage());
        }

        // 从数据库获取
        List<ChatMessage> dbMessages = chatMessageRepository.findByConversationIdOrderByCreatedAtAsc(conversationId);
        List<Map<String, String>> messages = new ArrayList<>();

        for (ChatMessage msg : dbMessages) {
            Map<String, String> msgMap = new HashMap<>();
            msgMap.put("role", msg.getRole());
            msgMap.put("content", msg.getContent());
            messages.add(msgMap);
        }

        // 更新Redis缓存
        if (!messages.isEmpty()) {
            updateConversationCache(conversationId, messages);
        }

        logger.debug("Retrieved conversation history from database: {}", conversationId);
        return messages;
    }

    /**
     * 更新会话缓存
     */
    private void updateConversationCache(String conversationId, List<Map<String, String>> messages) {
        String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
        try {
            redisUtil.set(cacheKey, messages, CACHE_EXPIRE_HOURS * 3600);
            logger.debug("Updated conversation cache: {}", conversationId);
        } catch (Exception e) {
            logger.warn("Failed to update conversation cache: {}", e.getMessage());
            // 备份到内存
            conversationHistory.put(conversationId, new ArrayList<>(messages));
        }
    }

    /**
     * 确保会话存在
     */
    private void ensureSessionExists(String conversationId, String userId) {
        try {
            if (!chatSessionRepository.existsByConversationId(conversationId)) {
                ChatSession session = new ChatSession(conversationId, userId, "新对话");
                chatSessionRepository.save(session);
                logger.info("Created new chat session: {}", conversationId);
            }
        } catch (Exception e) {
            logger.error("Failed to ensure session exists: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新会话信息
     */
    private void updateSessionInfo(String conversationId) {
        try {
            Optional<ChatSession> sessionOpt = chatSessionRepository.findByConversationId(conversationId);
            if (sessionOpt.isPresent()) {
                ChatSession session = sessionOpt.get();
                session.setMessageCount(session.getMessageCount() + 2); // 用户消息 + AI回复
                chatSessionRepository.save(session);
                logger.debug("Updated session info: {}", conversationId);
            }
        } catch (Exception e) {
            logger.error("Failed to update session info: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清除会话历史（从数据库、Redis和内存中清除）
     */
    @Transactional
    public void clearConversationHistory(String conversationId) {
        try {
            // 从数据库删除消息
            chatMessageRepository.deleteByConversationId(conversationId);

            // 标记会话为非活跃状态
            Optional<ChatSession> sessionOpt = chatSessionRepository.findByConversationId(conversationId);
            if (sessionOpt.isPresent()) {
                ChatSession session = sessionOpt.get();
                session.setIsActive(false);
                session.setMessageCount(0);
                chatSessionRepository.save(session);
            }

            // 从Redis缓存删除
            String cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
            redisUtil.del(cacheKey);

            // 从内存删除
            conversationHistory.remove(conversationId);

            logger.info("Cleared conversation history for: {}", conversationId);
        } catch (Exception e) {
            logger.error("Failed to clear conversation history: {}", e.getMessage(), e);
            throw new RuntimeException("清除会话历史失败", e);
        }
    }

    /**
     * 清除所有会话历史
     */
    @Transactional
    public void clearAllConversations() {
        try {
            // 清除所有会话的活跃状态
            List<ChatSession> activeSessions = chatSessionRepository.findByUserIdAndIsActiveOrderByUpdatedAtDesc("default_user", true);
            for (ChatSession session : activeSessions) {
                session.setIsActive(false);
                session.setMessageCount(0);
            }
            chatSessionRepository.saveAll(activeSessions);

            // 清除Redis缓存（通过模式匹配删除所有会话缓存）
            // 注意：这里简化处理，实际项目中可能需要更精确的缓存清理策略

            // 清除内存缓存
            conversationHistory.clear();

            logger.info("Cleared all conversation histories");
        } catch (Exception e) {
            logger.error("Failed to clear all conversations: {}", e.getMessage(), e);
            throw new RuntimeException("清除所有会话历史失败", e);
        }
    }
    
    /**
     * 获取活跃会话数量
     */
    public int getActiveConversationsCount() {
        return conversationHistory.size();
    }
}
