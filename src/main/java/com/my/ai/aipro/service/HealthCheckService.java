package com.my.ai.aipro.service;

import com.my.ai.aipro.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查服务
 */
@Service
public class HealthCheckService {

    private static final Logger logger = LoggerFactory.getLogger(HealthCheckService.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 检查所有服务的健康状态
     */
    public Map<String, Object> checkAllServices() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        // 检查数据库连接
        healthStatus.put("database", checkDatabaseHealth());
        
        // 检查Redis连接
        healthStatus.put("redis", checkRedisHealth());
        
        // 整体状态
        boolean allHealthy = healthStatus.values().stream()
                .allMatch(status -> status instanceof Map && "UP".equals(((Map<?, ?>) status).get("status")));
        
        healthStatus.put("overall", allHealthy ? "UP" : "DOWN");
        healthStatus.put("timestamp", System.currentTimeMillis());
        
        return healthStatus;
    }

    /**
     * 检查数据库健康状态
     */
    public Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) { // 5秒超时
                dbHealth.put("status", "UP");
                dbHealth.put("database", connection.getMetaData().getDatabaseProductName());
                dbHealth.put("url", connection.getMetaData().getURL());
            } else {
                dbHealth.put("status", "DOWN");
                dbHealth.put("error", "Connection validation failed");
            }
        } catch (Exception e) {
            logger.error("Database health check failed", e);
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }
        
        return dbHealth;
    }

    /**
     * 检查Redis健康状态
     */
    public Map<String, Object> checkRedisHealth() {
        Map<String, Object> redisHealth = new HashMap<>();
        
        try {
            // 尝试设置和获取一个测试键
            String testKey = "health_check_test";
            String testValue = "test_" + System.currentTimeMillis();
            
            redisUtil.set(testKey, testValue, 10); // 10秒过期
            Object retrieved = redisUtil.get(testKey);
            
            if (testValue.equals(retrieved)) {
                redisHealth.put("status", "UP");
                redisHealth.put("operation", "SET/GET test successful");
                // 清理测试键
                redisUtil.del(testKey);
            } else {
                redisHealth.put("status", "DOWN");
                redisHealth.put("error", "SET/GET test failed");
            }
        } catch (Exception e) {
            logger.error("Redis health check failed", e);
            redisHealth.put("status", "DOWN");
            redisHealth.put("error", e.getMessage());
        }
        
        return redisHealth;
    }

    /**
     * 检查DeepSeek API健康状态（简单检查）
     */
    public Map<String, Object> checkDeepSeekApiHealth() {
        Map<String, Object> apiHealth = new HashMap<>();
        
        try {
            // 这里可以添加对DeepSeek API的简单ping检查
            // 由于API调用需要消耗token，这里只做基本的连通性检查
            apiHealth.put("status", "UNKNOWN");
            apiHealth.put("note", "API health check not implemented to avoid token consumption");
        } catch (Exception e) {
            logger.error("DeepSeek API health check failed", e);
            apiHealth.put("status", "DOWN");
            apiHealth.put("error", e.getMessage());
        }
        
        return apiHealth;
    }
}
