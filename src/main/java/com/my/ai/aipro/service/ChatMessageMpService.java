package com.my.ai.aipro.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.ai.aipro.entity.ChatMessageMp;
import com.my.ai.aipro.mapper.ChatMessageMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天消息服务 - MyBatis-Plus版本
 */
@Service
public class ChatMessageMpService extends ServiceImpl<ChatMessageMapper, ChatMessageMp> {

    private static final Logger logger = LoggerFactory.getLogger(ChatMessageMpService.class);

    /**
     * 获取会话的所有消息
     */
    public List<ChatMessageMp> getConversationMessages(String conversationId) {
        return baseMapper.findByConversationIdOrderByCreatedAtAsc(conversationId);
    }

    /**
     * 获取会话的最近N条消息
     */
    public List<ChatMessageMp> getRecentMessages(String conversationId, int limit) {
        return baseMapper.findRecentMessagesByConversationId(conversationId, limit);
    }

    /**
     * 分页获取会话消息
     */
    public IPage<ChatMessageMp> getMessagesWithPagination(String conversationId, int current, int size) {
        Page<ChatMessageMp> page = new Page<>(current, size);
        return baseMapper.findMessagesByConversationId(page, conversationId);
    }

    /**
     * 保存用户消息
     */
    @Transactional
    public ChatMessageMp saveUserMessage(String conversationId, String content, Double temperature, Integer maxTokens, String model) {
        ChatMessageMp message = new ChatMessageMp(conversationId, "user", content);
        message.setTemperature(temperature);
        message.setMaxTokens(maxTokens);
        message.setModelUsed(model);
        save(message);
        logger.debug("Saved user message for conversation: {}", conversationId);
        return message;
    }

    /**
     * 保存AI回复消息
     */
    @Transactional
    public ChatMessageMp saveAssistantMessage(String conversationId, String content, String model, Integer tokensUsed) {
        ChatMessageMp message = new ChatMessageMp(conversationId, "assistant", content);
        message.setModelUsed(model);
        message.setTokensUsed(tokensUsed);
        save(message);
        logger.debug("Saved assistant message for conversation: {}", conversationId);
        return message;
    }

    /**
     * 批量保存消息
     */
    @Transactional
    public boolean batchSaveMessages(List<ChatMessageMp> messages) {
        return baseMapper.batchInsert(messages) > 0;
    }

    /**
     * 删除会话的所有消息
     */
    @Transactional
    public int deleteConversationMessages(String conversationId) {
        int count = baseMapper.deleteByConversationId(conversationId);
        logger.info("Deleted {} messages for conversation: {}", count, conversationId);
        return count;
    }

    /**
     * 统计会话消息数量
     */
    public long countConversationMessages(String conversationId) {
        return baseMapper.countByConversationId(conversationId);
    }

    /**
     * 获取会话的token使用总量
     */
    public Long getTotalTokensUsed(String conversationId) {
        return baseMapper.sumTokensUsedByConversationId(conversationId);
    }

    /**
     * 获取用户的token使用总量
     */
    public Long getUserTotalTokensUsed(String userId) {
        return baseMapper.sumTokensUsedByUserId(userId);
    }

    /**
     * 获取会话中最后一条消息
     */
    public ChatMessageMp getLastMessage(String conversationId) {
        return baseMapper.findLastMessageByConversationId(conversationId);
    }

    /**
     * 根据角色获取消息
     */
    public List<ChatMessageMp> getMessagesByRole(String conversationId, String role) {
        return baseMapper.findByConversationIdAndRole(conversationId, role);
    }

    /**
     * 搜索消息内容
     */
    public List<ChatMessageMp> searchMessages(String conversationId, String keyword) {
        return baseMapper.searchMessagesByKeyword(conversationId, keyword);
    }

    /**
     * 获取指定时间范围内的消息
     */
    public List<ChatMessageMp> getMessagesByTimeRange(String conversationId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findMessagesByTimeRange(conversationId, startTime, endTime);
    }

    /**
     * 统计各角色的消息数量
     */
    public List<Map<String, Object>> countMessagesByRole(String conversationId) {
        return baseMapper.countMessagesByRole(conversationId);
    }

    /**
     * 清理过期消息
     */
    @Transactional
    public int cleanupExpiredMessages(int daysAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysAgo);
        List<ChatMessageMp> expiredMessages = baseMapper.findMessagesCreatedBefore(cutoffTime);
        
        int count = 0;
        for (ChatMessageMp message : expiredMessages) {
            if (removeById(message.getId())) {
                count++;
            }
        }
        
        logger.info("Cleaned up {} expired messages", count);
        return count;
    }

    /**
     * 使用QueryWrapper进行复杂查询
     */
    public List<ChatMessageMp> findMessagesWithWrapper(String conversationId, String role, LocalDateTime startTime, Integer minTokens) {
        QueryWrapper<ChatMessageMp> wrapper = new QueryWrapper<>();
        wrapper.eq("conversation_id", conversationId)
               .eq(role != null, "role", role)
               .ge(startTime != null, "created_at", startTime)
               .ge(minTokens != null, "tokens_used", minTokens)
               .orderByAsc("created_at");
        
        return list(wrapper);
    }

    /**
     * 获取会话历史（转换为Map格式，兼容原有接口）
     */
    public List<Map<String, String>> getConversationHistoryAsMap(String conversationId) {
        List<ChatMessageMp> messages = getConversationMessages(conversationId);
        return messages.stream()
                .map(msg -> {
                    Map<String, String> map = new java.util.HashMap<>();
                    map.put("role", msg.getRole());
                    map.put("content", msg.getContent());
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取最近N条消息（转换为Map格式）
     */
    public List<Map<String, String>> getRecentMessagesAsMap(String conversationId, int limit) {
        List<ChatMessageMp> messages = getRecentMessages(conversationId, limit);
        // 反转顺序，因为查询是按时间倒序的
        java.util.Collections.reverse(messages);
        
        return messages.stream()
                .map(msg -> {
                    Map<String, String> map = new java.util.HashMap<>();
                    map.put("role", msg.getRole());
                    map.put("content", msg.getContent());
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
    }
}
