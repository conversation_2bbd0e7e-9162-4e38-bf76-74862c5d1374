package com.my.ai.aipro.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.ai.aipro.entity.ChatSessionMp;
import com.my.ai.aipro.mapper.ChatSessionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天会话服务 - MyBatis-Plus版本
 */
@Service
public class ChatSessionMpService extends ServiceImpl<ChatSessionMapper, ChatSessionMp> {

    private static final Logger logger = LoggerFactory.getLogger(ChatSessionMpService.class);

    /**
     * 根据会话ID查找会话
     */
    public ChatSessionMp findByConversationId(String conversationId) {
        return baseMapper.findByConversationId(conversationId);
    }

    /**
     * 分页查询用户的活跃会话
     */
    public IPage<ChatSessionMp> findActiveSessionsByUserId(String userId, int current, int size) {
        Page<ChatSessionMp> page = new Page<>(current, size);
        return baseMapper.findByUserIdAndIsActive(page, userId, true);
    }

    /**
     * 获取用户所有会话
     */
    public List<ChatSessionMp> findAllSessionsByUserId(String userId) {
        return baseMapper.findByUserId(userId);
    }

    /**
     * 创建新会话
     */
    @Transactional
    public ChatSessionMp createSession(String conversationId, String userId, String title) {
        ChatSessionMp session = new ChatSessionMp(conversationId, userId, title);
        save(session);
        logger.info("Created new chat session: {}", conversationId);
        return session;
    }

    /**
     * 确保会话存在
     */
    @Transactional
    public ChatSessionMp ensureSessionExists(String conversationId, String userId) {
        ChatSessionMp session = findByConversationId(conversationId);
        if (session == null) {
            session = createSession(conversationId, userId, "新对话");
        }
        return session;
    }

    /**
     * 增加消息数量
     */
    @Transactional
    public void incrementMessageCount(String conversationId, int increment) {
        baseMapper.incrementMessageCount(conversationId, increment);
        logger.debug("Incremented message count for session: {}", conversationId);
    }

    /**
     * 更新会话标题
     */
    @Transactional
    public boolean updateSessionTitle(String conversationId, String title) {
        ChatSessionMp session = findByConversationId(conversationId);
        if (session != null) {
            session.setTitle(title);
            return updateById(session);
        }
        return false;
    }

    /**
     * 停用会话
     */
    @Transactional
    public boolean deactivateSession(String conversationId) {
        ChatSessionMp session = findByConversationId(conversationId);
        if (session != null) {
            session.setIsActive(false);
            return updateById(session);
        }
        return false;
    }

    /**
     * 批量停用用户的所有会话
     */
    @Transactional
    public int deactivateAllUserSessions(String userId) {
        return baseMapper.updateActiveStatusByUserId(userId, false);
    }

    /**
     * 删除会话
     */
    @Transactional
    public boolean deleteSession(String conversationId) {
        return baseMapper.deleteByConversationId(conversationId) > 0;
    }

    /**
     * 获取用户最近的会话
     */
    public List<ChatSessionMp> getRecentSessions(String userId, int limit) {
        return baseMapper.findRecentSessions(userId, limit);
    }

    /**
     * 统计用户活跃会话数量
     */
    public long countActiveSessionsByUserId(String userId) {
        return baseMapper.countByUserIdAndIsActive(userId, true);
    }

    /**
     * 清理过期会话
     */
    @Transactional
    public int cleanupExpiredSessions(int daysAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysAgo);
        List<ChatSessionMp> expiredSessions = baseMapper.findSessionsUpdatedBefore(cutoffTime);
        
        int count = 0;
        for (ChatSessionMp session : expiredSessions) {
            if (removeById(session.getId())) {
                count++;
            }
        }
        
        logger.info("Cleaned up {} expired sessions", count);
        return count;
    }

    /**
     * 复杂条件查询
     */
    public List<ChatSessionMp> findSessionsByConditions(String userId, Boolean isActive, String title, 
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findSessionsByConditions(userId, isActive, title, startTime, endTime);
    }

    /**
     * 使用QueryWrapper进行复杂查询示例
     */
    public List<ChatSessionMp> findSessionsWithWrapper(String userId, String titleKeyword, Integer minMessageCount) {
        QueryWrapper<ChatSessionMp> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .eq("is_active", true)
               .like(titleKeyword != null, "title", titleKeyword)
               .ge(minMessageCount != null, "message_count", minMessageCount)
               .orderByDesc("updated_at");
        
        return list(wrapper);
    }

    /**
     * 分页查询所有会话
     */
    public IPage<ChatSessionMp> findSessionsWithPagination(int current, int size, String userId, Boolean isActive) {
        Page<ChatSessionMp> page = new Page<>(current, size);
        QueryWrapper<ChatSessionMp> wrapper = new QueryWrapper<>();
        
        if (userId != null) {
            wrapper.eq("user_id", userId);
        }
        if (isActive != null) {
            wrapper.eq("is_active", isActive);
        }
        
        wrapper.orderByDesc("updated_at");
        return page(page, wrapper);
    }
}
