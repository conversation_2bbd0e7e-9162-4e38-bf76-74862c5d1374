<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态资源测试</title>
    <link rel="stylesheet" href="/css/chat.css">
    <style>
        .test-container {
            padding: 20px;
            text-align: center;
        }
        .test-status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>静态资源加载测试</h1>
        
        <div id="css-test" class="test-status error">
            CSS文件加载失败
        </div>
        
        <div id="js-test" class="test-status error">
            JavaScript文件加载失败
        </div>
        
        <div class="test-info">
            <p>如果CSS正确加载，页面背景应该有渐变色</p>
            <p>如果JavaScript正确加载，上面的状态会变为成功</p>
        </div>
        
        <div>
            <a href="/">返回聊天页面</a>
        </div>
    </div>

    <script src="/js/chat.js"></script>
    <script>
        // 检查CSS是否加载
        window.addEventListener('load', function() {
            const body = window.getComputedStyle(document.body);
            const background = body.getPropertyValue('background-image');
            
            const cssTest = document.getElementById('css-test');
            if (background && background.includes('gradient')) {
                cssTest.textContent = 'CSS文件加载成功';
                cssTest.className = 'test-status success';
            }
            
            // 检查JS是否加载
            const jsTest = document.getElementById('js-test');
            if (typeof ChatApp !== 'undefined') {
                jsTest.textContent = 'JavaScript文件加载成功';
                jsTest.className = 'test-status success';
            }
        });
    </script>
</body>
</html>
