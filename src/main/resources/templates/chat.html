<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek AI 聊天助手</title>
    <link rel="stylesheet" href="/css/chat.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- 头部 -->
        <div class="chat-header">
            <div class="header-left">
                <i class="fas fa-robot"></i>
                <h1>DeepSeek AI 助手</h1>
            </div>
            <div class="header-right">
                <button id="clearBtn" class="btn btn-secondary" title="清除对话">
                    <i class="fas fa-trash"></i>
                </button>
                <button id="statusBtn" class="btn btn-info" title="查看状态">
                    <i class="fas fa-info-circle"></i>
                </button>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        你好！我是DeepSeek AI助手，很高兴为您服务。请问有什么可以帮助您的吗？
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="请输入您的消息..." 
                    rows="1"
                    maxlength="2000"></textarea>
                <button id="sendBtn" class="send-btn" title="发送消息">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-count">
                    <span id="charCount">0</span>/2000
                </span>
                <span class="conversation-id">
                    会话ID: <span id="conversationId">新会话</span>
                </span>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-indicator" id="loadingIndicator">
        <div class="loading-spinner"></div>
        <span>AI正在思考中...</span>
    </div>

    <!-- 状态模态框 -->
    <div class="modal" id="statusModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>系统状态</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="statusContent">
                <!-- 状态信息将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认操作</h3>
                <button class="modal-close" id="confirmModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要清除当前对话吗？此操作无法撤销。</p>
                <div class="modal-actions">
                    <button id="confirmYes" class="btn btn-danger">确定</button>
                    <button id="confirmNo" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/chat.js"></script>
</body>
</html>
