<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.ai.aipro.mapper.ChatMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.ai.aipro.entity.ChatMessageMp">
        <id column="id" property="id" />
        <result column="conversation_id" property="conversationId" />
        <result column="role" property="role" />
        <result column="content" property="content" />
        <result column="created_at" property="createdAt" />
        <result column="tokens_used" property="tokensUsed" />
        <result column="model_used" property="modelUsed" />
        <result column="temperature" property="temperature" />
        <result column="max_tokens" property="maxTokens" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, conversation_id, role, content, created_at, tokens_used, model_used, temperature, max_tokens
    </sql>

    <!-- 获取消息统计信息 -->
    <select id="getMessageStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_messages,
            COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
            COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
            SUM(COALESCE(tokens_used, 0)) as total_tokens,
            AVG(COALESCE(tokens_used, 0)) as avg_tokens_per_message,
            MIN(created_at) as first_message_time,
            MAX(created_at) as last_message_time
        FROM chat_messages 
        WHERE conversation_id = #{conversationId}
    </select>

    <!-- 获取热门词汇（基于消息内容） -->
    <select id="getPopularWords" resultType="java.util.Map">
        SELECT 
            word,
            COUNT(*) as frequency
        FROM (
            SELECT 
                unnest(string_to_array(lower(regexp_replace(content, '[^a-zA-Z\u4e00-\u9fa5\s]', '', 'g')), ' ')) as word
            FROM chat_messages 
            WHERE conversation_id = #{conversationId}
            AND role = 'user'
            AND length(content) > 0
        ) words
        WHERE length(word) > 2
        GROUP BY word
        ORDER BY frequency DESC
        LIMIT #{limit}
    </select>

    <!-- 按时间段统计消息数量 -->
    <select id="getMessageCountByHour" resultType="java.util.Map">
        SELECT 
            EXTRACT(HOUR FROM created_at) as hour,
            COUNT(*) as message_count
        FROM chat_messages 
        WHERE conversation_id = #{conversationId}
        AND created_at >= #{startDate}
        AND created_at <= #{endDate}
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
    </select>

    <!-- 获取长消息（超过指定长度的消息） -->
    <select id="getLongMessages" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_messages 
        WHERE conversation_id = #{conversationId}
        AND length(content) > #{minLength}
        ORDER BY length(content) DESC, created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 全文搜索消息（支持多关键词） -->
    <select id="fullTextSearch" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_messages 
        WHERE conversation_id = #{conversationId}
        <if test="keywords != null and keywords.size() > 0">
            AND (
            <foreach collection="keywords" item="keyword" separator=" OR ">
                lower(content) LIKE CONCAT('%', lower(#{keyword}), '%')
            </foreach>
            )
        </if>
        <if test="role != null and role != ''">
            AND role = #{role}
        </if>
        <if test="startDate != null">
            AND created_at >= #{startDate}
        </if>
        <if test="endDate != null">
            AND created_at <= #{endDate}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 获取相似消息（基于内容长度和关键词） -->
    <select id="getSimilarMessages" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_messages 
        WHERE conversation_id = #{conversationId}
        AND id != #{messageId}
        AND role = (SELECT role FROM chat_messages WHERE id = #{messageId})
        AND ABS(length(content) - (SELECT length(content) FROM chat_messages WHERE id = #{messageId})) <= #{lengthTolerance}
        ORDER BY ABS(length(content) - (SELECT length(content) FROM chat_messages WHERE id = #{messageId}))
        LIMIT #{limit}
    </select>

    <!-- 批量插入消息（优化版本） -->
    <insert id="batchInsertOptimized" parameterType="java.util.List">
        INSERT INTO chat_messages (conversation_id, role, content, created_at, tokens_used, model_used, temperature, max_tokens)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.conversationId},
                #{item.role},
                #{item.content},
                COALESCE(#{item.createdAt}, NOW()),
                #{item.tokensUsed},
                #{item.modelUsed},
                #{item.temperature},
                #{item.maxTokens}
            )
        </foreach>
    </insert>

    <!-- 清理过期消息（软删除或物理删除） -->
    <delete id="cleanupOldMessages">
        DELETE FROM chat_messages 
        WHERE created_at < #{cutoffDate}
        <if test="conversationIds != null and conversationIds.size() > 0">
            AND conversation_id IN
            <foreach collection="conversationIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <!-- 获取消息摘要（每个会话的最新消息） -->
    <select id="getMessageSummary" resultType="java.util.Map">
        SELECT 
            m1.conversation_id,
            m1.content as last_message,
            m1.role as last_message_role,
            m1.created_at as last_message_time,
            s.title as session_title
        FROM chat_messages m1
        INNER JOIN (
            SELECT conversation_id, MAX(created_at) as max_time
            FROM chat_messages
            WHERE conversation_id IN
            <foreach collection="conversationIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            GROUP BY conversation_id
        ) m2 ON m1.conversation_id = m2.conversation_id AND m1.created_at = m2.max_time
        LEFT JOIN chat_sessions s ON m1.conversation_id = s.conversation_id
        ORDER BY m1.created_at DESC
    </select>

</mapper>
