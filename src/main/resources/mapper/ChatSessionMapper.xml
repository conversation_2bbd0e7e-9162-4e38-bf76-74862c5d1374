<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.ai.aipro.mapper.ChatSessionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.ai.aipro.entity.ChatSessionMp">
        <id column="id" property="id" />
        <result column="conversation_id" property="conversationId" />
        <result column="user_id" property="userId" />
        <result column="title" property="title" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="message_count" property="messageCount" />
        <result column="is_active" property="isActive" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, conversation_id, user_id, title, created_at, updated_at, message_count, is_active
    </sql>

    <!-- 复杂查询：获取会话统计信息 -->
    <select id="getSessionStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN is_active = true THEN 1 END) as active_sessions,
            COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_sessions,
            SUM(message_count) as total_messages,
            AVG(message_count) as avg_messages_per_session
        FROM chat_sessions 
        WHERE user_id = #{userId}
    </select>

    <!-- 获取用户最活跃的会话 -->
    <select id="getMostActiveSession" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_sessions 
        WHERE user_id = #{userId} AND is_active = true
        ORDER BY message_count DESC, updated_at DESC
        LIMIT 1
    </select>

    <!-- 批量更新会话状态 -->
    <update id="batchUpdateStatus">
        UPDATE chat_sessions 
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE conversation_id IN
        <foreach collection="conversationIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据时间范围查询会话 -->
    <select id="findSessionsByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_sessions 
        WHERE user_id = #{userId}
        <if test="startDate != null">
            AND created_at >= #{startDate}
        </if>
        <if test="endDate != null">
            AND created_at <= #{endDate}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 获取会话详情（包含消息统计） -->
    <select id="getSessionWithMessageStats" resultType="java.util.Map">
        SELECT 
            s.*,
            COALESCE(m.message_count, 0) as actual_message_count,
            COALESCE(m.user_messages, 0) as user_message_count,
            COALESCE(m.assistant_messages, 0) as assistant_message_count,
            COALESCE(m.total_tokens, 0) as total_tokens_used
        FROM chat_sessions s
        LEFT JOIN (
            SELECT 
                conversation_id,
                COUNT(*) as message_count,
                COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
                COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
                SUM(COALESCE(tokens_used, 0)) as total_tokens
            FROM chat_messages 
            GROUP BY conversation_id
        ) m ON s.conversation_id = m.conversation_id
        WHERE s.conversation_id = #{conversationId}
    </select>

    <!-- 搜索会话 -->
    <select id="searchSessions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_sessions 
        WHERE user_id = #{userId}
        <if test="keyword != null and keyword != ''">
            AND (
                title LIKE CONCAT('%', #{keyword}, '%')
                OR conversation_id LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY 
            CASE WHEN title LIKE CONCAT('%', #{keyword}, '%') THEN 1 ELSE 2 END,
            updated_at DESC
    </select>

</mapper>
