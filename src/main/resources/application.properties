# ??????
spring.application.name=AIPRO
server.port=8080

# DeepSeek AI ??
deepseek.api.key=***********************************
deepseek.api.url=https://api.deepseek.com/v1/chat/completions
deepseek.model=deepseek-chat

# ????
logging.level.com.my.ai.aipro=INFO
logging.level.org.springframework.web=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Web??
spring.mvc.static-path-pattern=/static/**
spring.web.resources.static-locations=classpath:/static/

# Thymeleaf??
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# HTTP????
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true