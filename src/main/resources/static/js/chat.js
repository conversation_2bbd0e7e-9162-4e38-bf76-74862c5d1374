// 聊天应用主类
class ChatApp {
    constructor() {
        this.conversationId = null;
        this.isLoading = false;
        this.initializeElements();
        this.bindEvents();
        this.initializeWelcomeMessage();
    }

    // 初始化DOM元素
    initializeElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.statusBtn = document.getElementById('statusBtn');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.charCount = document.getElementById('charCount');
        this.conversationIdSpan = document.getElementById('conversationId');
        
        // 模态框元素
        this.statusModal = document.getElementById('statusModal');
        this.confirmModal = document.getElementById('confirmModal');
        this.modalClose = document.getElementById('modalClose');
        this.confirmModalClose = document.getElementById('confirmModalClose');
        this.confirmYes = document.getElementById('confirmYes');
        this.confirmNo = document.getElementById('confirmNo');
        this.statusContent = document.getElementById('statusContent');
        this.confirmMessage = document.getElementById('confirmMessage');
    }

    // 绑定事件
    bindEvents() {
        // 发送消息
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 字符计数
        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.autoResize();
        });

        // 清除对话
        this.clearBtn.addEventListener('click', () => this.showConfirmDialog());

        // 查看状态
        this.statusBtn.addEventListener('click', () => this.showStatus());

        // 模态框关闭
        this.modalClose.addEventListener('click', () => this.hideModal(this.statusModal));
        this.confirmModalClose.addEventListener('click', () => this.hideModal(this.confirmModal));
        this.confirmNo.addEventListener('click', () => this.hideModal(this.confirmModal));
        this.confirmYes.addEventListener('click', () => {
            this.clearConversation();
            this.hideModal(this.confirmModal);
        });

        // 点击模态框背景关闭
        this.statusModal.addEventListener('click', (e) => {
            if (e.target === this.statusModal) this.hideModal(this.statusModal);
        });
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) this.hideModal(this.confirmModal);
        });
    }

    // 初始化欢迎消息时间
    initializeWelcomeMessage() {
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = this.formatTime(new Date());
        }
    }

    // 发送消息
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;

        // 添加用户消息到界面
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.updateCharCount();
        this.autoResize();

        // 显示加载状态
        this.setLoading(true);

        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    conversationId: this.conversationId
                })
            });

            const data = await response.json();

            if (data.success) {
                // 更新会话ID
                if (!this.conversationId) {
                    this.conversationId = data.conversationId;
                    this.updateConversationId();
                }

                // 添加AI回复到界面
                this.addMessage(data.message, 'bot');
            } else {
                this.addMessage(`错误: ${data.error}`, 'bot', true);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('抱歉，发送消息时出现错误，请稍后重试。', 'bot', true);
        } finally {
            this.setLoading(false);
        }
    }

    // 添加消息到界面
    addMessage(text, sender, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        const textDiv = document.createElement('div');
        textDiv.className = 'message-text';
        if (isError) {
            textDiv.style.color = '#dc3545';
        }
        textDiv.textContent = text;

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = this.formatTime(new Date());

        contentDiv.appendChild(textDiv);
        contentDiv.appendChild(timeDiv);
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    // 设置加载状态
    setLoading(loading) {
        this.isLoading = loading;
        this.sendBtn.disabled = loading;
        this.loadingIndicator.style.display = loading ? 'flex' : 'none';
    }

    // 更新字符计数
    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = count;
        
        if (count > 1800) {
            this.charCount.style.color = '#dc3545';
        } else if (count > 1500) {
            this.charCount.style.color = '#ffc107';
        } else {
            this.charCount.style.color = '#6c757d';
        }
    }

    // 自动调整输入框高度
    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    // 滚动到底部
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    // 格式化时间
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 更新会话ID显示
    updateConversationId() {
        if (this.conversationId) {
            const shortId = this.conversationId.substring(0, 8) + '...';
            this.conversationIdSpan.textContent = shortId;
        } else {
            this.conversationIdSpan.textContent = '新会话';
        }
    }

    // 显示确认对话框
    showConfirmDialog() {
        this.confirmMessage.textContent = '确定要清除当前对话吗？此操作无法撤销。';
        this.showModal(this.confirmModal);
    }

    // 清除对话
    async clearConversation() {
        if (!this.conversationId) {
            // 如果没有会话ID，直接清除界面
            this.clearChatMessages();
            return;
        }

        try {
            const response = await fetch(`/api/chat/history/${this.conversationId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.clearChatMessages();
                this.conversationId = null;
                this.updateConversationId();
            } else {
                alert('清除对话失败，请稍后重试。');
            }
        } catch (error) {
            console.error('清除对话失败:', error);
            alert('清除对话失败，请稍后重试。');
        }
    }

    // 清除聊天消息界面
    clearChatMessages() {
        // 保留欢迎消息，清除其他消息
        const messages = this.chatMessages.querySelectorAll('.message');
        messages.forEach((message, index) => {
            if (index > 0) { // 跳过第一条欢迎消息
                message.remove();
            }
        });
    }

    // 显示状态
    async showStatus() {
        try {
            const response = await fetch('/api/chat/status');
            const data = await response.json();

            this.statusContent.innerHTML = `
                <div style="line-height: 1.6;">
                    <p><strong>服务状态:</strong> ${data.status || '未知'}</p>
                    <p><strong>活跃会话数:</strong> ${data.activeConversations || 0}</p>
                    <p><strong>当前会话ID:</strong> ${this.conversationId || '无'}</p>
                    <p><strong>更新时间:</strong> ${new Date(data.timestamp).toLocaleString('zh-CN')}</p>
                </div>
            `;
        } catch (error) {
            console.error('获取状态失败:', error);
            this.statusContent.innerHTML = '<p style="color: #dc3545;">获取状态信息失败</p>';
        }

        this.showModal(this.statusModal);
    }

    // 显示模态框
    showModal(modal) {
        modal.style.display = 'flex';
    }

    // 隐藏模态框
    hideModal(modal) {
        modal.style.display = 'none';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
