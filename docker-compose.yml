version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: aipro_postgres
    environment:
      POSTGRES_DB: aipro_db
      POSTGRES_USER: aipro_user
      POSTGRES_PASSWORD: aipro_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - aipro_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: aipro_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aipro_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Redis Commander (可选的Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aipro_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - aipro_network
    depends_on:
      - redis
    restart: unless-stopped

  # pgAdmin (可选的PostgreSQL管理界面)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aipro_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8082:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - aipro_network
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  aipro_network:
    driver: bridge
