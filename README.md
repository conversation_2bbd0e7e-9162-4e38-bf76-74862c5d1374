# DeepSeek AI 聊天助手

这是一个基于Spring Boot和DeepSeek AI的智能聊天应用，提供了友好的Web界面和RESTful API。

## 功能特性

- 🤖 **智能对话**: 基于DeepSeek AI模型的智能对话功能
- 💬 **会话管理**: 支持多轮对话，自动维护上下文
- 🎨 **现代界面**: 响应式设计，支持移动端和桌面端
- 📊 **状态监控**: 实时查看系统状态和会话信息
- 🔄 **实时交互**: 流畅的用户体验，支持实时消息发送
- 🧹 **会话清理**: 支持清除单个或所有会话历史
- 🗄️ **数据持久化**: PostgreSQL数据库存储会话和消息
- ⚡ **Redis缓存**: 高性能缓存提升响应速度
- 🔍 **健康监控**: 完整的系统健康检查功能
- 🚀 **MyBatis-Plus**: 强大的ORM框架，支持代码生成和复杂查询
- 📊 **数据分析**: 丰富的统计和分析功能
- 🔧 **代码生成**: 自动生成Entity、Mapper、Service代码

## 技术栈

- **后端**: Spring Boot 2.7.18, Spring Data JPA, MyBatis-Plus *******
- **前端**: HTML5, CSS3, JavaScript (ES6+), Thymeleaf
- **数据库**: PostgreSQL 13
- **缓存**: Redis 7
- **ORM框架**: MyBatis-Plus (支持代码生成、分页、条件构造器)
- **AI模型**: DeepSeek Chat API
- **构建工具**: Maven
- **Java版本**: 1.8+
- **容器化**: Docker & Docker Compose

## 快速开始

### 1. 环境要求

- Java 1.8 或更高版本
- Maven 3.6 或更高版本
- Docker 和 Docker Compose（推荐）
- PostgreSQL 13+（如果不使用Docker）
- Redis 7+（如果不使用Docker）
- DeepSeek API密钥

### 2. 启动数据库和缓存服务

使用Docker Compose快速启动PostgreSQL和Redis：

```bash
# 启动数据库和缓存服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

这将启动以下服务：
- PostgreSQL: localhost:5432
- Redis: localhost:6379
- pgAdmin: http://localhost:8082 (<EMAIL> / admin123)
- Redis Commander: http://localhost:8081

### 3. 配置应用

在 `src/main/resources/application.properties` 文件中配置：

```properties
# DeepSeek API配置
deepseek.api.key=your-deepseek-api-key-here

# 数据库配置（如果使用不同的配置）
spring.datasource.url=*****************************************
spring.datasource.username=aipro_user
spring.datasource.password=aipro_password

# Redis配置（如果使用不同的配置）
spring.redis.host=localhost
spring.redis.port=6379
```

### 4. 运行应用

```bash
# 克隆项目
git clone <repository-url>
cd AIPRO

# 编译并运行
./mvnw spring-boot:run

# 或者使用Maven
mvn spring-boot:run
```

### 5. 访问应用

- **主应用**: http://localhost:8080
- **健康检查**: http://localhost:8080/api/chat/health/detailed
- **测试页面**: http://localhost:8080/test-static

## API文档

### 标准聊天接口 (JPA版本)

#### 发送消息
```http
POST /api/chat/send
```

### MyBatis-Plus增强接口

#### 发送消息 (增强版)
```http
POST /api/chat-mp/send
```

#### 分页获取用户会话
```http
GET /api/chat-mp/sessions?userId=default_user&current=1&size=10
```

#### 分页获取会话消息
```http
GET /api/chat-mp/messages/{conversationId}?current=1&size=20
```

#### 获取会话详细信息
```http
GET /api/chat-mp/session/{conversationId}
```

#### 更新会话标题
```http
PUT /api/chat-mp/session/{conversationId}/title
Content-Type: application/json

{
    "title": "新的会话标题"
}
```

#### 搜索消息
```http
GET /api/chat-mp/search/{conversationId}?keyword=关键词
```

#### 获取会话统计
```http
GET /api/chat-mp/stats/{conversationId}
```

返回示例：
```json
{
    "messageCount": 10,
    "tokensUsed": 500,
    "roleStats": [
        {"role": "user", "count": 5},
        {"role": "assistant", "count": 5}
    ],
    "lastMessage": {
        "id": 123,
        "role": "assistant",
        "content": "最后一条消息内容",
        "createdAt": "2024-01-01T12:00:00"
    }
}
```

### 聊天相关接口

#### 发送消息
```http
POST /api/chat/send
Content-Type: application/json

{
    "message": "你好，请介绍一下自己",
    "conversationId": "optional-conversation-id",
    "temperature": 0.7,
    "maxTokens": 1000
}
```

#### 获取会话历史
```http
GET /api/chat/history/{conversationId}
```

#### 清除会话历史
```http
DELETE /api/chat/history/{conversationId}
```

#### 清除所有会话
```http
DELETE /api/chat/history
```

#### 系统状态
```http
GET /api/chat/status
```

#### 健康检查
```http
GET /api/chat/health
```

#### 详细健康检查
```http
GET /api/chat/health/detailed
```

返回示例：
```json
{
  "database": {
    "status": "UP",
    "database": "PostgreSQL",
    "url": "*****************************************"
  },
  "redis": {
    "status": "UP",
    "operation": "SET/GET test successful"
  },
  "overall": "UP",
  "timestamp": 1703123456789
}
```

## 项目结构

```
src/
├── main/
│   ├── java/com/my/ai/aipro/
│   │   ├── controller/          # 控制器层
│   │   │   ├── AiChatController.java
│   │   │   └── WebController.java
│   │   ├── service/             # 服务层
│   │   │   └── DeepSeekChatService.java
│   │   ├── dto/                 # 数据传输对象
│   │   │   ├── ChatRequest.java
│   │   │   └── ChatResponse.java
│   │   └── AiproApplication.java
│   └── resources/
│       ├── static/              # 静态资源
│       │   ├── css/chat.css
│       │   └── js/chat.js
│       ├── templates/           # 模板文件
│       │   └── chat.html
│       └── application.properties
└── test/                        # 测试文件
```

## 配置说明

### application.properties 主要配置项

```properties
# 服务端口
server.port=8080

# DeepSeek AI配置
spring.ai.openai.api-key=your-api-key
spring.ai.openai.base-url=https://api.deepseek.com/v1
spring.ai.openai.chat.options.model=deepseek-chat
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=1000

# 日志级别
logging.level.com.my.ai.aipro=INFO
```

## 使用说明

1. **开始对话**: 在输入框中输入消息，点击发送按钮或按Enter键
2. **查看状态**: 点击右上角的信息按钮查看系统状态
3. **清除对话**: 点击右上角的垃圾桶按钮清除当前对话
4. **多行输入**: 使用Shift+Enter可以换行输入
5. **字符限制**: 单条消息最多支持2000个字符

## 开发指南

### 运行测试

```bash
./mvnw test
```

### 构建项目

```bash
./mvnw clean package
```

### 开发模式

在开发模式下，可以启用热重载：

```bash
./mvnw spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.thymeleaf.cache=false"
```

## 注意事项

1. 请确保您的DeepSeek API密钥有效且有足够的配额
2. 会话历史存储在内存中，重启应用后会丢失
3. 生产环境建议使用数据库或Redis存储会话历史
4. 建议设置适当的API调用频率限制

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
