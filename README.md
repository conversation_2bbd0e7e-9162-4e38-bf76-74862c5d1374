# DeepSeek AI 聊天助手

这是一个基于Spring Boot和DeepSeek AI的智能聊天应用，提供了友好的Web界面和RESTful API。

## 功能特性

- 🤖 **智能对话**: 基于DeepSeek AI模型的智能对话功能
- 💬 **会话管理**: 支持多轮对话，自动维护上下文
- 🎨 **现代界面**: 响应式设计，支持移动端和桌面端
- 📊 **状态监控**: 实时查看系统状态和会话信息
- 🔄 **实时交互**: 流畅的用户体验，支持实时消息发送
- 🧹 **会话清理**: 支持清除单个或所有会话历史

## 技术栈

- **后端**: Spring Boot 3.5.4, Spring AI
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **AI模型**: DeepSeek Chat
- **构建工具**: Maven
- **Java版本**: 17

## 快速开始

### 1. 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本
- DeepSeek API密钥

### 2. 配置API密钥

在 `src/main/resources/application.properties` 文件中配置您的DeepSeek API密钥：

```properties
spring.ai.openai.api-key=your-deepseek-api-key-here
```

### 3. 运行应用

```bash
# 克隆项目
git clone <repository-url>
cd AIPRO

# 编译并运行
./mvnw spring-boot:run

# 或者使用Maven
mvn spring-boot:run
```

### 4. 访问应用

打开浏览器访问: http://localhost:8080

## API文档

### 聊天相关接口

#### 发送消息
```http
POST /api/chat/send
Content-Type: application/json

{
    "message": "你好，请介绍一下自己",
    "conversationId": "optional-conversation-id",
    "temperature": 0.7,
    "maxTokens": 1000
}
```

#### 获取会话历史
```http
GET /api/chat/history/{conversationId}
```

#### 清除会话历史
```http
DELETE /api/chat/history/{conversationId}
```

#### 清除所有会话
```http
DELETE /api/chat/history
```

#### 系统状态
```http
GET /api/chat/status
```

#### 健康检查
```http
GET /api/chat/health
```

## 项目结构

```
src/
├── main/
│   ├── java/com/my/ai/aipro/
│   │   ├── controller/          # 控制器层
│   │   │   ├── AiChatController.java
│   │   │   └── WebController.java
│   │   ├── service/             # 服务层
│   │   │   └── DeepSeekChatService.java
│   │   ├── dto/                 # 数据传输对象
│   │   │   ├── ChatRequest.java
│   │   │   └── ChatResponse.java
│   │   └── AiproApplication.java
│   └── resources/
│       ├── static/              # 静态资源
│       │   ├── css/chat.css
│       │   └── js/chat.js
│       ├── templates/           # 模板文件
│       │   └── chat.html
│       └── application.properties
└── test/                        # 测试文件
```

## 配置说明

### application.properties 主要配置项

```properties
# 服务端口
server.port=8080

# DeepSeek AI配置
spring.ai.openai.api-key=your-api-key
spring.ai.openai.base-url=https://api.deepseek.com/v1
spring.ai.openai.chat.options.model=deepseek-chat
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=1000

# 日志级别
logging.level.com.my.ai.aipro=INFO
```

## 使用说明

1. **开始对话**: 在输入框中输入消息，点击发送按钮或按Enter键
2. **查看状态**: 点击右上角的信息按钮查看系统状态
3. **清除对话**: 点击右上角的垃圾桶按钮清除当前对话
4. **多行输入**: 使用Shift+Enter可以换行输入
5. **字符限制**: 单条消息最多支持2000个字符

## 开发指南

### 运行测试

```bash
./mvnw test
```

### 构建项目

```bash
./mvnw clean package
```

### 开发模式

在开发模式下，可以启用热重载：

```bash
./mvnw spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.thymeleaf.cache=false"
```

## 注意事项

1. 请确保您的DeepSeek API密钥有效且有足够的配额
2. 会话历史存储在内存中，重启应用后会丢失
3. 生产环境建议使用数据库或Redis存储会话历史
4. 建议设置适当的API调用频率限制

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
